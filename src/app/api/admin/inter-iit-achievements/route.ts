import { NextRequest, NextResponse } from 'next/server';
import { getAllInterIITAchievements, createInterIITAchievement } from '@/lib/inter-iit-achievements-storage';

export async function GET() {
  try {
    const achievements = await getAllInterIITAchievements();
    const achievementsArray = Object.values(achievements).sort((a, b) => 
      new Date(b.achievementDate).getTime() - new Date(a.achievementDate).getTime()
    );
    return NextResponse.json(achievementsArray);
  } catch (error) {
    console.error('Error fetching Inter-IIT achievements:', error);
    return NextResponse.json(
      { error: 'Failed to fetch Inter-IIT achievements' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = [
      'achievementType', 'competitionName', 'interIITEdition', 'year', 
      'hostIIT', 'location', 'achievementDescription', 'significance', 
      'competitionCategory', 'achievementDate', 'status'
    ];
    
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }
    
    // Ensure arrays are properly initialized
    const achievementData = {
      ...body,
      teamMembers: body.teamMembers || [],
      supportingDocuments: body.supportingDocuments || []
    };
    
    const newAchievement = await createInterIITAchievement(achievementData);
    return NextResponse.json(newAchievement, { status: 201 });
  } catch (error) {
    console.error('Error creating Inter-IIT achievement:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create Inter-IIT achievement' },
      { status: 500 }
    );
  }
}
